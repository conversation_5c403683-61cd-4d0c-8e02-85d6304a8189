{"name": "be", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "ts-node-dev --respawn src/index.ts", "build": "tsc", "start": "node dist/index.js"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"express": "^5.1.0"}, "devDependencies": {"@types/express": "^5.0.3", "@types/node": "^24.0.15", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}